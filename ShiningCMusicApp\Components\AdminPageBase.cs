using Microsoft.AspNetCore.Components;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Components
{
    public class AdminPageBase : ComponentBase
    {
        [Inject] protected AppConfiguration AppConfig { get; set; } = default!;

        /// <summary>
        /// Gets whether action button labels should be shown based on configuration
        /// </summary>
        protected bool ShowActionButtonLabel => AppConfig?.ShowActionButtonLabel ?? true;
    }
}
