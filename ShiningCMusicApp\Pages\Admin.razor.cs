using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicApp.Components;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Pages;

public partial class AdminBase : AdminPageBase
{
    [Inject] protected IUserApiService UserApi { get; set; } = default!;
    [Inject] protected ISubjectApiService SubjectApi { get; set; } = default!;
    [Inject] protected ILocationApiService LocationApi { get; set; } = default!;
    [Inject] protected ITutorApiService TutorApi { get; set; } = default!;
    [Inject] protected IStudentApiService StudentApi { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected IDialogService DialogService { get; set; } = default!;

    // Data properties
    protected List<User> users = new();
    protected List<UserRole> userRoles = new();
    protected List<Subject> subjects = new();
    protected List<Location> locations = new();
    protected List<Tutor> tutors = new();
    protected List<Student> students = new();
    protected bool isLoading = true;

    // User modal variables
    protected bool showUserModal = false;
    protected bool isEditUserMode = false;
    protected string userModalTitle = string.Empty;
    protected User currentUser = new();
    protected int? selectedTutorId = null;
    protected int? selectedStudentId = null;
    protected bool showAssignmentSection = false;
    protected List<string> passwordValidationErrors = new();

    // UserRole modal variables
    protected bool showUserRoleModal = false;
    protected bool isEditUserRoleMode = false;
    protected string userRoleModalTitle = string.Empty;
    protected UserRole currentUserRole = new();

    // Subject modal variables
    protected bool showSubjectModal = false;
    protected bool isEditSubjectMode = false;
    protected bool isSaving = false;
    protected string subjectModalTitle = string.Empty;
    protected Subject currentSubject = new();

    // Location modal variables
    protected bool showLocationModal = false;
    protected bool isEditLocationMode = false;
    protected string locationModalTitle = string.Empty;
    protected Location currentLocation = new();

    protected string activeBreakpoint = "Large";
    protected string columnWidth = "300";
    protected string gridKey = Guid.NewGuid().ToString();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    protected async Task LoadData()
    {
        isLoading = true;
        try
        {
            var usersTask = UserApi.GetUsersAsync();
            var userRolesTask = UserApi.GetUserRolesAsync();
            var subjectsTask = SubjectApi.GetSubjectsAsync();
            var locationsTask = LocationApi.GetLocationsAsync();
            var tutorsTask = TutorApi.GetTutorsAsync();
            var studentsTask = StudentApi.GetStudentsAsync();

            await Task.WhenAll(usersTask, userRolesTask, subjectsTask, locationsTask, tutorsTask, studentsTask);

            users = await usersTask;
            userRoles = await userRolesTask;
            subjects = await subjectsTask;
            locations = await locationsTask;
            tutors = await tutorsTask;
            students = await studentsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {users.Count} users, {userRoles.Count} roles, {subjects.Count} subjects and {locations.Count} locations");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading data", ex.Message);
        }
        finally
        {
            isLoading = false;
        }
    }

    protected async Task RefreshData()
    {
        await LoadData();
    }

    // Subject CRUD Methods
    protected void OpenCreateSubjectModal()
    {
        currentSubject = new Subject();
        isEditSubjectMode = false;
        subjectModalTitle = "Create New Subject";
        showSubjectModal = true;
    }

    protected void OpenEditSubjectModal(Subject? subject)
    {
        if (subject != null)
        {
            currentSubject = new Subject
            {
                SubjectId = subject.SubjectId,
                SubjectName = subject.SubjectName
            };

            isEditSubjectMode = true;
            subjectModalTitle = "Edit Subject";
            showSubjectModal = true;
        }
    }

    protected void CloseSubjectModal()
    {
        showSubjectModal = false;
        currentSubject = new();
        isSaving = false;
    }

    protected async Task SaveSubject()
    {
        if (string.IsNullOrWhiteSpace(currentSubject.SubjectName))
        {
            await DialogService.ShowWarningAsync("Subject name is required.", "Please enter a valid subject name before saving.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditSubjectMode)
            {
                success = await SubjectApi.UpdateSubjectAsync(currentSubject.SubjectId, currentSubject);
            }
            else
            {
                var createdSubject = await SubjectApi.CreateSubjectAsync(currentSubject);
                success = createdSubject != null;
            }

            if (success)
            {
                CloseSubjectModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save subject", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving subject: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving subject", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteSubject(Subject? subject)
    {
        if (subject == null) return;

        var message = $"Are you sure you want to delete subject '{subject.SubjectName}'?";
        var details = "This action cannot be undone.";

        var confirmed = await DialogService.ShowDeleteConfirmationAsync(
            message,
            details,
            "Delete Subject");

        if (confirmed)
        {
            try
            {
                var success = await SubjectApi.DeleteSubjectAsync(subject.SubjectId);
                if (success)
                {
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete subject", "Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting subject: {ex.Message}");
                await DialogService.ShowErrorAsync("Error deleting subject", ex.Message);
            }
        }
    }

    // Location CRUD Methods
    protected void OpenCreateLocationModal()
    {
        currentLocation = new Location();
        isEditLocationMode = false;
        locationModalTitle = "Create New Location";
        showLocationModal = true;
    }

    protected void OpenEditLocationModal(Location? location)
    {
        if (location != null)
        {
            currentLocation = new Location
            {
                LocationId = location.LocationId,
                LocationName = location.LocationName
            };

            isEditLocationMode = true;
            locationModalTitle = "Edit Location";
            showLocationModal = true;
        }
    }

    protected void CloseLocationModal()
    {
        showLocationModal = false;
        currentLocation = new();
        isSaving = false;
    }

    protected async Task SaveLocation()
    {
        if (string.IsNullOrWhiteSpace(currentLocation.LocationName))
        {
            await DialogService.ShowWarningAsync("Location name is required.", "Please enter a valid location name before saving.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditLocationMode)
            {
                success = await LocationApi.UpdateLocationAsync(currentLocation.LocationId, currentLocation);
            }
            else
            {
                var createdLocation = await LocationApi.CreateLocationAsync(currentLocation);
                success = createdLocation != null;
            }

            if (success)
            {
                CloseLocationModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save location", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving location: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving location", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteLocation(Location? location)
    {
        if (location == null) return;

        var message = $"Are you sure you want to delete location '{location.LocationName}'?";
        var details = "This action cannot be undone.";

        var confirmed = await DialogService.ShowDeleteConfirmationAsync(
            message,
            details,
            "Delete Location");

        if (confirmed)
        {
            try
            {
                var success = await LocationApi.DeleteLocationAsync(location.LocationId);
                if (success)
                {
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete location", "Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting location: {ex.Message}");
                await DialogService.ShowErrorAsync("Error deleting location", ex.Message);
            }
        }
    }

    protected async Task OnBreakpointChanged()
    {
        try
        {
            if (activeBreakpoint == "Small")
            {
                columnWidth = "150";
            }
            else if (activeBreakpoint == "Medium" || activeBreakpoint == "Large")
            {
                columnWidth = "300";
            }
            gridKey = Guid.NewGuid().ToString();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnBreakPointChanged: {ex.Message}");
        }
    }
}
